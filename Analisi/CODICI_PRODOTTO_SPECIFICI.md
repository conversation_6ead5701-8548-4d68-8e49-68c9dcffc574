# CODICI PRODOTTO SPECIFICI - ANALISI DETTAGLIATA

## Panoramica

Analisi dei codici prodotto specifici richiesti nel sistema NAW. Sono stati identificati riferimenti diretti nel codice per alcuni di questi prodotti con logiche particolari.

## Codici Prodotto Richiesti

### 📋 **Lista Completa**
- **32D T** ✅ - Trovato nel codice
- **M103F** ❌ - Non trovato riferimenti diretti
- **342ST** ❌ - Non trovato riferimenti diretti  
- **UL03F** ❌ - Non trovato riferimenti diretti
- **344ST** ❌ - Non trovato riferimenti diretti
- **550QT** ❌ - Non trovato riferimenti diretti
- **32F T** ❌ - Non trovato riferimenti diretti
- **M119** ❌ - Non trovato riferimenti diretti
- **348ST** ❌ - Non trovato riferimenti diretti
- **UL06** ❌ - Non trovato riferimenti diretti
- **558QT** ❌ - Non trovato riferimenti diretti
- **M106F** ❌ - Non trovato riferimenti diretti

## Riferimenti Trovati nel Codice

### 🎯 **32D T - Prodotto con Gestione Speciale**

**Ubicazione**: `Source/Services.Common.Impl/src/it/sistinf/albedoweb/services/interfacce/collettore/service/impl/CollettoreImpl.java`

<augment_code_snippet path="Source/Services.Common.Impl/src/it/sistinf/albedoweb/services/interfacce/collettore/service/impl/CollettoreImpl.java" mode="EXCERPT">
````java
private boolean isFlagPostel(PolizzaInfo polizzaInfo, Mdtab010Rec0Info mdtab010Rec0, PercipientiInfo percipiente){
    String goodJob = isGoodJob(mdtab010Rec0);
    String codProdotto = GenericUtils.stringToString(polizzaInfo.getCodProdotto());
    
    if (SrvConstants.SI.equals(goodJob) ||
        (SrvConstants.ZLII == percipiente.getCodSocieta() && Integer.valueOf(percipiente.getNumeroCategoria())> 12) ||
        "M".equals(GenericUtils.stringToString(percipiente.getModalPagam())) ||
         "644QT".equals(codProdotto) || "644RT".equals(codProdotto) || "32C T".equals(codProdotto) ||
         "32D T".equals(codProdotto) || "P350".equals(codProdotto) || "P351".equals(codProdotto) || "P354".equals(codProdotto) ||
         "P355".equals(codProdotto) || "P356".equals(codProdotto) || "P366".equals(codProdotto)
        ) {
        return false;  // NON è un prodotto Poste Italiane
    }
    return true;       // È un prodotto Poste Italiane
}
````
</augment_code_snippet>

#### **Funzionalità Identificata**
- **Metodo**: `isFlagPostel()` - Determina se un prodotto è gestito da Poste Italiane
- **Logica**: Il prodotto **32D T** è **ESCLUSO** dalla gestione Poste Italiane
- **Contesto**: Interfaccia Collettore per liquidazioni e imposte
- **Significato**: Prodotto con canale distributivo diverso da Poste Italiane

### 🏦 **Altri Prodotti Correlati Identificati**

Nella stessa logica sono stati trovati altri codici prodotto con comportamento simile:

#### **Prodotti NON Poste Italiane**
- **644QT** - Prodotto con gestione speciale
- **644RT** - Prodotto con gestione speciale  
- **32C T** - Prodotto correlato a 32D T
- **32D T** - Prodotto richiesto ✅
- **P350** - Prodotto serie P
- **P351** - Prodotto serie P
- **P354** - Prodotto serie P
- **P355** - Prodotto serie P
- **P356** - Prodotto serie P
- **P366** - Prodotto serie P

## Analisi Tipologica dei Codici

### 🔍 **Pattern Identificati**

#### **1. Codici Serie "32X T"**
- **32C T** - Trovato nel codice
- **32D T** - Trovato nel codice ✅
- **32F T** - Richiesto ma non trovato

**Caratteristiche**:
- Formato: `32[lettera] T`
- Gestione speciale per canale distributivo
- Esclusione da logiche Poste Italiane

#### **2. Codici Serie "M###F"**
- **M103F** - Richiesto ma non trovato
- **M106F** - Richiesto ma non trovato  
- **M119** - Richiesto ma non trovato

**Caratteristiche**:
- Formato: `M[numero]F` o `M[numero]`
- Probabilmente prodotti Multigaranzia
- Prefisso "M" indica tipologia Multigaranzia

#### **3. Codici Serie "UL##F"**
- **UL03F** - Richiesto ma non trovato
- **UL06** - Richiesto ma non trovato

**Caratteristiche**:
- Formato: `UL[numero][F]`
- Prefisso "UL" indica Unit Linked
- Probabilmente prodotti Unit Linked specifici

#### **4. Codici Serie "###ST"**
- **342ST** - Richiesto ma non trovato
- **344ST** - Richiesto ma non trovato
- **348ST** - Richiesto ma non trovato

**Caratteristiche**:
- Formato: `[numero]ST`
- Suffisso "ST" potrebbe indicare "Standard" o tipologia specifica

#### **5. Codici Serie "###QT"**
- **550QT** - Richiesto ma non trovato
- **558QT** - Richiesto ma non trovato
- **644QT** - Trovato nel codice (correlato)

**Caratteristiche**:
- Formato: `[numero]QT`
- Suffisso "QT" potrebbe indicare "Quote" o tipologia specifica

## Logiche di Business Identificate

### 🎯 **Gestione Canale Distributivo**

Il metodo `isFlagPostel()` implementa una logica di esclusione per determinare quali prodotti NON sono gestiti da Poste Italiane:

```java
// Condizioni per NON essere Poste Italiane:
1. goodJob = "SI" (flag specifico)
2. Società ZLII con categoria > 12
3. Modalità pagamento = "M"
4. Codici prodotto specifici (incluso 32D T)
```

### 📊 **Impatto Funzionale**

#### **Per il prodotto 32D T**:
- **Liquidazioni**: Gestione diversa da prodotti Poste Italiane
- **Imposte**: Calcolo specifico per canale non-Poste
- **Collettore**: Interfaccia dedicata per questo tipo di prodotti
- **Percipiente**: Gestione speciale del percipiente

## Codici Unit Linked Correlati

### 🔍 **Riferimenti UL nel Codice**

Sono stati trovati riferimenti a codici Unit Linked simili:

<augment_code_snippet path="Source/Services.Common.Impl/src/it/sistinf/albedoweb/services/modelloMatematico/formula/service/impl/ModelloMatematicoFormulaImpl.java" mode="EXCERPT">
````java
// Formula COSTO SWITCH per Unit Linked
if ("940".equals(GenericUtils.stringToString(formula))) {
    for (PosizioneInfo posizioneInfo : listaPoszioni) {				
        if (GenericUtils.stringToString(posizioneInfo.getRecordPosizione().getT024UlFondo())!=null) {
            String codiceUT = GenericUtils.stringToString(posizioneInfo.getRecordPosizione().getCodiceUt());
            if ("U401".equals(codiceUT) || "U451".equals(codiceUT) || "U614".equals(codiceUT) || "U664".equals(codiceUT) ||
                "U601".equals(codiceUT) || "U611".equals(codiceUT) || "U651".equals(codiceUT) || "U661".equals(codiceUT) ) {
                // Gestione speciale per questi codici UT
                retList = listaPoszioni;
            } else {
                retList = new ArrayList<PosizioneInfo>();
                retList.add(posizioneInfo);
            }
        }
    }
}
````
</augment_code_snippet>

#### **Codici UT Unit Linked Identificati**:
- **U401**, **U451**, **U614**, **U664**
- **U601**, **U611**, **U651**, **U661**

Questi potrebbero essere correlati ai codici UL richiesti.

## Enum e Tipologie Prodotto

### 📋 **Tipologie Identificate nel Codice**

<augment_code_snippet path="Source/REST.Services.Cobol.Model/src/it/sistinf/rest/cobol/model/proposta/datiidentificativi/ProdottoInfo.java" mode="EXCERPT">
````java
public enum TipoProdottoEnum {
    M(String.valueOf("M")),     // Multigaranzia
    U(String.valueOf("U")),     // Unit Linked  
    A(String.valueOf("A")),     // Altro
    P(String.valueOf("P")),     // Pensionistico
    Z(String.valueOf("Z")),     // Speciale
    R(String.valueOf("R")),     // Rendita
    EMPTY(String.valueOf(""));
}
````
</augment_code_snippet>

### 🎯 **Correlazione con Codici Richiesti**

- **M103F, M106F, M119** → Probabilmente tipo **M** (Multigaranzia)
- **UL03F, UL06** → Probabilmente tipo **U** (Unit Linked)
- **342ST, 344ST, 348ST** → Tipo da determinare
- **550QT, 558QT** → Tipo da determinare
- **32D T, 32F T** → Gestione speciale canale distributivo

## Raccomandazioni per Ricerca Approfondita

### 🔍 **Dove Cercare i Codici Mancanti**

1. **Database di Prodotto**:
   - Tabelle T004 (Prodotti)
   - Tabelle T010 (Tariffe/Garanzie)
   - Configurazioni prodotto specifiche

2. **File di Configurazione**:
   - Properties files per prodotti
   - XML di configurazione COBOL
   - Mapping prodotto-programma

3. **Documentazione Business**:
   - Manuali prodotto
   - Specifiche tecniche
   - Documentazione legacy

4. **Codice Legacy**:
   - Programmi COBOL mainframe
   - Stored procedures database
   - Batch jobs di elaborazione

### 📊 **Query Database Suggerite**

```sql
-- Ricerca codici prodotto
SELECT * FROM T004_PRODOTTI 
WHERE COD_PRODOTTO IN ('32D T','M103F','342ST','UL03F','344ST','550QT',
                       '32F T','M119','348ST','UL06','558QT','M106F');

-- Ricerca per pattern
SELECT * FROM T004_PRODOTTI 
WHERE COD_PRODOTTO LIKE '32_ T' 
   OR COD_PRODOTTO LIKE 'M___F' 
   OR COD_PRODOTTO LIKE 'UL__F'
   OR COD_PRODOTTO LIKE '___ST'
   OR COD_PRODOTTO LIKE '___QT';
```

## Conclusioni

### ✅ **Risultati Ottenuti**
1. **32D T** identificato con logica specifica per canale distributivo
2. Pattern di naming identificati per le diverse serie
3. Logiche di business correlate individuate
4. Codici simili trovati nel sistema

### ❌ **Codici Non Trovati**
11 dei 12 codici richiesti non hanno riferimenti diretti nel codice Java analizzato, ma potrebbero essere:
- Configurati in database
- Gestiti in programmi COBOL
- Presenti in file di configurazione non analizzati
- Prodotti legacy o futuri

### 🎯 **Prossimi Passi**
1. Analisi database prodotti
2. Ricerca in programmi COBOL mainframe  
3. Verifica file di configurazione XML/Properties
4. Consultazione documentazione business
